import { Auth0Provider } from '@auth0/auth0-react'
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { RouterProvider } from 'react-router'
import { createBrowserRouter } from 'react-router-dom'

import { routes } from '@/routes'

import './main.css'

const router = createBrowserRouter(routes)

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <Auth0Provider
      domain="skrin.us.auth0.com"
      clientId="V4N364MJiex0MhaJPjaBx2EseLnBn5MQ"
      authorizationParams={{ redirect_uri: window.location.origin }}
    >
      <RouterProvider router={router} />
    </Auth0Provider>
    ,
  </StrictMode>,
)
