import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

export function Start() {
  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle>Spa y veterinaria Mascotas</CardTitle>
        <CardDescription>Bienvenido a la veterinatia</CardDescription>
      </CardHeader>
      <CardContent>
        <form>
          <div className="flex flex-col gap-6">
            <div className="grid gap-3">
              <Label htmlFor="identifier">Documento de identidad</Label>
              <Input
                id="identifier"
                type="identifier"
                placeholder="Ingresa tu número de DNI"
                required
              />
            </div>
            <div className="grid gap-3">
              <Label htmlFor="reason">Motivo de atención</Label>
              <Select defaultValue="reason1">
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="reason1">Consulta general</SelectItem>
                  <SelectItem value="reason2">Control de vacunación</SelectItem>
                  <SelectItem value="reason3">Consulta preventiva</SelectItem>
                  <SelectItem value="reason4">Servicio de limpieza</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-3">
              <Button type="submit" className="w-full">
                Continuar
              </Button>
            </div>
          </div>
          <div className="mt-4 text-center text-sm">
            Esta información nos ayuda a personalizar tu experiencia
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
