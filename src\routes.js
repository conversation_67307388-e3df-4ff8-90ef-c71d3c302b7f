import { lazy } from 'react'

import { Basic } from '@/layouts/basic'
import { Centered } from '@/layouts/centered'
import { Empty } from '@/layouts/empty'
import { NotFound } from '@/pages/error'

/**
 * @type {import('react-router-dom').RouteObject[]}
 */
export const routes = [
  {
    path: '/',
    Component: Basic,
    ErrorBoundary: NotFound,
    children: [
      {
        index: true,
        Component: lazy(() => import('@/pages/home')),
      },
      {
        path: 'settings',
        Component: lazy(() => import('@/pages/settings')),
      },
    ],
  },
  {
    path: 'login',
    Component: Empty,
    children: [
      {
        index: true,
        Component: lazy(() => import('@/pages/login')),
      },
    ],
  },
  {
    path: 'start',
    Component: Centered,
    children: [
      {
        index: true,
        Component: lazy(() => import('@/pages/start')),
      },
    ],
  },
]
